@extends('layouts.supplier')

@section('title', 'Tambah Pengiriman - Dashboard Supplier')
@section('page-title', 'Tambah Pengiriman')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Tambah Pengiriman Baru</h1>
                    <p class="text-gray-600 mt-1">Buat pengiriman produk ke gudang</p>
                </div>
                <a href="{{ route('supplier.deliveries.index') }}" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Ke<PERSON>li
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Informasi Pengiriman</h2>
        </div>
        <div class="supplier-dashboard-card-content">
            <form method="POST" action="{{ route('supplier.deliveries.store') }}" class="space-y-6">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Warehouse Admin -->
                    <div>
                        <label for="warehouse_admin_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Kirim ke Admin Gudang <span class="text-red-500">*</span>
                        </label>
                        <select id="warehouse_admin_id"
                                name="warehouse_admin_id"
                                class="supplier-dashboard-select @error('warehouse_admin_id') supplier-dashboard-input-error @enderror"
                                required>
                            <option value="">Pilih Admin Gudang</option>
                            @foreach($warehouseAdmins as $admin)
                                <option value="{{ $admin->id }}" {{ old('warehouse_admin_id') == $admin->id ? 'selected' : '' }}>
                                    {{ $admin->name }} ({{ $admin->email }})
                                </option>
                            @endforeach
                        </select>
                        @error('warehouse_admin_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Product -->
                    <div>
                        <label for="product_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Produk <span class="text-red-500">*</span>
                        </label>
                        <select id="product_id"
                                name="product_id"
                                class="supplier-dashboard-select @error('product_id') supplier-dashboard-input-error @enderror"
                                required>
                            <option value="">Pilih Produk</option>
                            @foreach($products as $product)
                                <option value="{{ $product->id }}" {{ old('product_id') == $product->id ? 'selected' : '' }}>
                                    {{ $product->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('product_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Quantity -->
                    <div>
                        <label for="quantity" class="block text-sm font-medium text-gray-700 mb-2">
                            Jumlah <span class="text-red-500">*</span>
                        </label>
                        <input type="number"
                               id="quantity"
                               name="quantity"
                               value="{{ old('quantity') }}"
                               min="1"
                               class="supplier-dashboard-input @error('quantity') supplier-dashboard-input-error @enderror"
                               placeholder="Masukkan jumlah"
                               required>
                        @error('quantity')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Unit Price -->
                    <div>
                        <label for="unit_price" class="block text-sm font-medium text-gray-700 mb-2">
                            Harga Satuan (Opsional)
                        </label>
                        <input type="number"
                               id="unit_price"
                               name="unit_price"
                               value="{{ old('unit_price') }}"
                               min="0"
                               step="0.01"
                               class="supplier-dashboard-input @error('unit_price') supplier-dashboard-input-error @enderror"
                               placeholder="Masukkan harga satuan">
                        @error('unit_price')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Delivery Date -->
                    <div>
                        <label for="delivery_date" class="block text-sm font-medium text-gray-700 mb-2">
                            Tanggal Pengiriman <span class="text-red-500">*</span>
                        </label>
                        <input type="date"
                               id="delivery_date"
                               name="delivery_date"
                               value="{{ old('delivery_date', date('Y-m-d')) }}"
                               class="supplier-dashboard-input @error('delivery_date') supplier-dashboard-input-error @enderror"
                               required>
                        @error('delivery_date')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Total Price Display -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Total Harga (Otomatis)
                        </label>
                        <input type="text"
                               id="total_price_display"
                               class="supplier-dashboard-input bg-gray-50"
                               placeholder="Akan dihitung otomatis"
                               readonly>
                    </div>
                </div>

                <!-- Notes -->
                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                        Catatan (Opsional)
                    </label>
                    <textarea id="notes"
                              name="notes"
                              rows="4"
                              class="supplier-dashboard-textarea @error('notes') supplier-dashboard-input-error @enderror"
                              placeholder="Tambahkan catatan untuk pengiriman ini...">{{ old('notes') }}</textarea>
                    @error('notes')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Form Actions -->
                <div class="flex flex-col sm:flex-row gap-3 pt-6 border-t border-gray-200">
                    <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Simpan Pengiriman
                    </button>
                    <a href="{{ route('supplier.deliveries.index') }}" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Batal
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const quantityInput = document.getElementById('quantity');
    const unitPriceInput = document.getElementById('unit_price');
    const totalPriceDisplay = document.getElementById('total_price_display');
    
    function calculateTotal() {
        const quantity = parseFloat(quantityInput.value) || 0;
        const unitPrice = parseFloat(unitPriceInput.value) || 0;
        const total = quantity * unitPrice;
        
        if (total > 0) {
            totalPriceDisplay.value = 'Rp ' + total.toLocaleString('id-ID', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        } else {
            totalPriceDisplay.value = '';
        }
    }
    
    quantityInput.addEventListener('input', calculateTotal);
    unitPriceInput.addEventListener('input', calculateTotal);
    
    // Calculate on page load if values exist
    calculateTotal();
});
</script>
@endpush
@endsection
